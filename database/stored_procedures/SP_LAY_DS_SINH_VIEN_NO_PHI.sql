-- =====================================================
-- Stored Procedure: SP_LAY_DS_SINH_VIEN_NO_PHI
-- Mục đích: <PERSON><PERSON><PERSON> danh sách sinh viên nợ phí với phân trang
-- Thay thế cho query phức tạp LayDsSinhVienNoPhiPaging
-- =====================================================

DELIMITER $$

DROP PROCEDURE IF EXISTS SP_LAY_DS_SINH_VIEN_NO_PHI$$

CREATE PROCEDURE SP_LAY_DS_SINH_VIEN_NO_PHI(
    IN p_id_nganh_hoc INT,
    IN p_id_khoa_hoc INT, 
    IN p_id_lop INT,
    IN p_id_tinh_trang INT,
    IN p_keyword VARCHAR(255),
    IN p_page_number INT,
    IN p_page_size INT,
    OUT p_total_records INT
)
BEGIN
    DECLARE v_offset INT DEFAULT 0;
    
    -- Tính offset cho phân trang
    SET v_offset = p_page_number * p_page_size;
    
    -- Tạo bảng tạm để lưu kết quả
    DROP TEMPORARY TABLE IF EXISTS temp_sinh_vien_no_phi;
    
    CREATE TEMPORARY TABLE temp_sinh_vien_no_phi (
        ID_SINH_VIEN INT,
        MA_SINH_VIEN VARCHAR(20),
        HO_TEN VARCHAR(255),
        NU TINYINT(1),
        NGAY_SINH DATE,
        SDT_SINH_VIEN VARCHAR(15),
        DIA_CHI TEXT,
        MA_LOP VARCHAR(20),
        TEN_LOP VARCHAR(255),
        THANH_TIEN DECIMAL(15,2),
        LA_SV_BAO_LUU TINYINT(1),
        LOAI_PHI VARCHAR(20)
    );
    
    -- 1. Thêm sinh viên nợ học phí thường (HOC_PHI)
    INSERT INTO temp_sinh_vien_no_phi
    SELECT 
        sv.ID_SINH_VIEN,
        sv.MA_SINH_VIEN,
        CONCAT(sv.HO_LOT, ' ', sv.TEN) as HO_TEN,
        sv.NU,
        sv.NGAY_SINH,
        sv.SDT_SINH_VIEN,
        CASE 
            WHEN sv.ID_XA IS NULL THEN sv.DIA_CHI 
            ELSE CONCAT(sv.DIA_CHI, ', ', dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) 
        END as DIA_CHI,
        l.MA_LOP,
        l.TEN_LOP,
        hp.MUC_HOC_PHI as THANH_TIEN,
        sv.LA_SV_BAO_LUU,
        'HOC_PHI' as LOAI_PHI
    FROM SINH_VIEN sv
    LEFT JOIN TINH_TRANG tt ON tt.ID_TINH_TRANG = sv.ID_TINH_TRANG
    LEFT JOIN LOP l ON sv.ID_LOP = l.ID_LOP
    LEFT JOIN KHOI k ON l.ID_KHOI = k.ID_KHOI
    LEFT JOIN HOC_PHI hp ON hp.ID_KHOI = k.ID_KHOI
    LEFT JOIN SV_DONG_HP dhp ON dhp.ID_HOC_PHI = hp.ID_HOC_PHI AND dhp.ID_SINH_VIEN = sv.ID_SINH_VIEN
    LEFT JOIN DM_XA dmXa ON sv.ID_XA = dmXa.ID_XA
    LEFT JOIN DM_HUYEN dmHuyen ON dmXa.ID_HUYEN = dmHuyen.ID_HUYEN
    LEFT JOIN DM_TINH dmTinh ON dmHuyen.ID_TINH = dmTinh.ID_TINH
    WHERE (dhp.ID_CAN_BO IS NULL OR dhp.huy = 1)
        AND hp.ID_HOC_PHI IS NOT NULL
        AND hp.MUC_HOC_PHI > 0
        AND tt.HOAT_DONG = 1
        AND sv.KHOA = 0
        AND (p_id_tinh_trang = -1 OR sv.ID_TINH_TRANG = p_id_tinh_trang)
        AND (p_id_nganh_hoc = -1 OR k.ID_NGANH_HOC = p_id_nganh_hoc)
        AND (p_id_khoa_hoc = -1 OR k.ID_NIEN_KHOA = p_id_khoa_hoc)
        AND (p_id_lop = -1 OR l.ID_LOP = p_id_lop)
        AND (p_keyword IS NULL OR p_keyword = '' OR 
             CONCAT(TRIM(sv.HO_LOT), ' ', sv.TEN) LIKE CONCAT('%', p_keyword, '%') OR 
             sv.MA_SINH_VIEN LIKE CONCAT('%', p_keyword, '%'));
    
    -- 2. Thêm sinh viên nợ phí khác (PHI_KHAC)
    INSERT INTO temp_sinh_vien_no_phi
    SELECT 
        sv.ID_SINH_VIEN,
        sv.MA_SINH_VIEN,
        CONCAT(sv.HO_LOT, ' ', sv.TEN) as HO_TEN,
        sv.NU,
        sv.NGAY_SINH,
        sv.SDT_SINH_VIEN,
        CASE 
            WHEN sv.ID_XA IS NULL THEN sv.DIA_CHI 
            ELSE CONCAT(sv.DIA_CHI, ', ', dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) 
        END as DIA_CHI,
        l.MA_LOP,
        l.TEN_LOP,
        IF(pk.NGAY_KET_THUC IS NOT NULL, 
           IF(CURDATE() < pk.NGAY_BAT_DAU, 
              PERIOD_DIFF(DATE_FORMAT(pk.NGAY_KET_THUC,'%Y%m'), DATE_FORMAT(pk.NGAY_BAT_DAU,'%Y%m')) + 1,
              PERIOD_DIFF(DATE_FORMAT(pk.NGAY_KET_THUC,'%Y%m'), DATE_FORMAT(CURDATE(),'%Y%m')) + 1) * pk.DON_GIA,
           pk.DON_GIA) as THANH_TIEN,
        sv.LA_SV_BAO_LUU,
        'PHI_KHAC' as LOAI_PHI
    FROM SINH_VIEN sv
    LEFT JOIN TINH_TRANG tt ON tt.ID_TINH_TRANG = sv.ID_TINH_TRANG
    LEFT JOIN LOP l ON sv.ID_LOP = l.ID_LOP
    LEFT JOIN KHOI k ON l.ID_KHOI = k.ID_KHOI
    LEFT JOIN PHI_KHAC pk ON pk.ID_KHOI = k.ID_KHOI
    LEFT JOIN SV_DONG_PK dpk ON dpk.ID_PHI_KHAC = pk.ID_PHI_KHAC AND dpk.ID_SINH_VIEN = sv.ID_SINH_VIEN
    LEFT JOIN DM_XA dmXa ON sv.ID_XA = dmXa.ID_XA
    LEFT JOIN DM_HUYEN dmHuyen ON dmXa.ID_HUYEN = dmHuyen.ID_HUYEN
    LEFT JOIN DM_TINH dmTinh ON dmHuyen.ID_TINH = dmTinh.ID_TINH
    WHERE (dpk.ID_CAN_BO IS NULL OR dpk.huy = 1)
        AND pk.ID_PHI_KHAC IS NOT NULL
        AND tt.HOAT_DONG = 1
        AND sv.KHOA = 0
        AND (p_id_tinh_trang = -1 OR sv.ID_TINH_TRANG = p_id_tinh_trang)
        AND (p_id_nganh_hoc = -1 OR k.ID_NGANH_HOC = p_id_nganh_hoc)
        AND (p_id_khoa_hoc = -1 OR k.ID_NIEN_KHOA = p_id_khoa_hoc)
        AND (p_id_lop = -1 OR l.ID_LOP = p_id_lop)
        AND (p_keyword IS NULL OR p_keyword = '' OR 
             CONCAT(TRIM(sv.HO_LOT), ' ', sv.TEN) LIKE CONCAT('%', p_keyword, '%') OR 
             sv.MA_SINH_VIEN LIKE CONCAT('%', p_keyword, '%'))
        AND (pk.NGAY_BAT_DAU IS NULL OR pk.NGAY_KET_THUC IS NULL OR 
             (pk.NGAY_BAT_DAU IS NOT NULL AND pk.NGAY_KET_THUC IS NOT NULL AND CURDATE() < pk.NGAY_KET_THUC));
    
    -- 3. Thêm sinh viên nợ phí học lại (HOC_LAI)
    INSERT INTO temp_sinh_vien_no_phi
    SELECT 
        sv.ID_SINH_VIEN,
        sv.MA_SINH_VIEN,
        CONCAT(sv.HO_LOT, ' ', sv.TEN) as HO_TEN,
        sv.NU,
        sv.NGAY_SINH,
        sv.SDT_SINH_VIEN,
        CASE 
            WHEN sv.ID_XA IS NULL THEN sv.DIA_CHI 
            ELSE CONCAT(sv.DIA_CHI, ', ', dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) 
        END as DIA_CHI,
        l.MA_LOP,
        l.TEN_LOP,
        (CASE 
            WHEN mh.ID_LOAI_MON_HOC = 1 THEN hp.GIA_HOC_LAI_LT * n.SO__VHT
            WHEN mh.ID_LOAI_MON_HOC = 2 THEN hp.GIA_HOC_LAI_TH * n.SO__VHT
            ELSE hp.GIA_HOC_LAI_TT * n.SO__VHT 
        END) as THANH_TIEN,
        sv.LA_SV_BAO_LUU,
        'HOC_LAI' as LOAI_PHI
    FROM NHOM n
    JOIN MON_HOC mh ON n.ID_MON_HOC = mh.ID_MON_HOC
    JOIN SV_HOC_NHOM h ON n.ID_NHOM = h.ID_NHOM
    JOIN SINH_VIEN sv ON h.ID_SINH_VIEN = sv.ID_SINH_VIEN
    LEFT JOIN TINH_TRANG tt ON tt.ID_TINH_TRANG = sv.ID_TINH_TRANG
    JOIN LOP l ON sv.ID_LOP = l.ID_LOP
    JOIN KHOI k ON l.ID_KHOI = k.ID_KHOI
    LEFT JOIN DM_XA dmXa ON sv.ID_XA = dmXa.ID_XA
    LEFT JOIN DM_HUYEN dmHuyen ON dmXa.ID_HUYEN = dmHuyen.ID_HUYEN
    LEFT JOIN DM_TINH dmTinh ON dmHuyen.ID_TINH = dmTinh.ID_TINH
    JOIN HOC_PHI hp ON hp.ID_HOC_KY = n.ID_HOC_KY AND hp.ID_KHOI = l.ID_KHOI
    LEFT JOIN SV_DONG_PHI_HOC_LAI dhphl ON sv.ID_SINH_VIEN = dhphl.ID_SINH_VIEN AND n.ID_NHOM = dhphl.ID_NHOM
    WHERE n.HOC_LAI = 1
        AND (dhphl.ID_CAN_BO IS NULL OR dhphl.huy = 1)
        AND tt.HOAT_DONG = 1
        AND sv.KHOA = 0
        AND (p_id_tinh_trang = -1 OR sv.ID_TINH_TRANG = p_id_tinh_trang)
        AND (p_id_nganh_hoc = -1 OR k.ID_NGANH_HOC = p_id_nganh_hoc)
        AND (p_id_khoa_hoc = -1 OR k.ID_NIEN_KHOA = p_id_khoa_hoc)
        AND (p_id_lop = -1 OR l.ID_LOP = p_id_lop)
        AND (p_keyword IS NULL OR p_keyword = '' OR 
             CONCAT(TRIM(sv.HO_LOT), ' ', sv.TEN) LIKE CONCAT('%', p_keyword, '%') OR 
             sv.MA_SINH_VIEN LIKE CONCAT('%', p_keyword, '%'));
    
    -- 4. Thêm sinh viên nợ phí thi lại (THI_LAI)
    INSERT INTO temp_sinh_vien_no_phi
    SELECT 
        sv.ID_SINH_VIEN,
        sv.MA_SINH_VIEN,
        CONCAT(sv.HO_LOT, ' ', sv.TEN) as HO_TEN,
        sv.NU,
        sv.NGAY_SINH,
        sv.SDT_SINH_VIEN,
        CASE 
            WHEN sv.ID_XA IS NULL THEN sv.DIA_CHI 
            ELSE CONCAT(sv.DIA_CHI, ', ', dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) 
        END as DIA_CHI,
        l.MA_LOP,
        l.TEN_LOP,
        (CASE 
            WHEN mh.ID_LOAI_MON_HOC = 1 THEN hp.GIA_THI_LAI_LT
            WHEN mh.ID_LOAI_MON_HOC = 2 THEN hp.GIA_THI_LAI_TH
            ELSE hp.GIA_THI_LAI_TT 
        END) as THANH_TIEN,
        sv.LA_SV_BAO_LUU,
        'THI_LAI' as LOAI_PHI
    FROM THI_LAI tl
    JOIN SINH_VIEN sv ON tl.ID_SINH_VIEN = sv.ID_SINH_VIEN
    LEFT JOIN TINH_TRANG tt ON tt.ID_TINH_TRANG = sv.ID_TINH_TRANG
    JOIN LOP l ON sv.ID_LOP = l.ID_LOP
    JOIN KHOI k ON l.ID_KHOI = k.ID_KHOI
    LEFT JOIN DM_XA dmXa ON sv.ID_XA = dmXa.ID_XA
    LEFT JOIN DM_HUYEN dmHuyen ON dmXa.ID_HUYEN = dmHuyen.ID_HUYEN
    LEFT JOIN DM_TINH dmTinh ON dmHuyen.ID_TINH = dmTinh.ID_TINH
    JOIN HOC_PHI hp ON hp.ID_HOC_KY = tl.ID_HOC_KY AND hp.ID_KHOI = l.ID_KHOI
    JOIN MON_HOC mh ON mh.ID_MON_HOC = tl.ID_MON_HOC
    WHERE (tl.ID_CAN_BO IS NULL OR tl.huy = 1)
        AND tt.HOAT_DONG = 1
        AND sv.KHOA = 0
        AND (p_id_tinh_trang = -1 OR sv.ID_TINH_TRANG = p_id_tinh_trang)
        AND (p_id_nganh_hoc = -1 OR k.ID_NGANH_HOC = p_id_nganh_hoc)
        AND (p_id_khoa_hoc = -1 OR k.ID_NIEN_KHOA = p_id_khoa_hoc)
        AND (p_id_lop = -1 OR l.ID_LOP = p_id_lop)
        AND (p_keyword IS NULL OR p_keyword = '' OR 
             CONCAT(TRIM(sv.HO_LOT), ' ', sv.TEN) LIKE CONCAT('%', p_keyword, '%') OR 
             sv.MA_SINH_VIEN LIKE CONCAT('%', p_keyword, '%'));
    
    -- Tính tổng số bản ghi
    SELECT COUNT(DISTINCT ID_SINH_VIEN) INTO p_total_records FROM temp_sinh_vien_no_phi;
    
    -- Trả về kết quả với phân trang và tổng tiền theo sinh viên
    SELECT 
        ID_SINH_VIEN,
        MA_SINH_VIEN,
        HO_TEN,
        NU,
        NGAY_SINH,
        SDT_SINH_VIEN,
        DIA_CHI,
        MA_LOP,
        TEN_LOP,
        SUM(THANH_TIEN) as TONG_TIEN,
        LA_SV_BAO_LUU
    FROM temp_sinh_vien_no_phi
    GROUP BY ID_SINH_VIEN, MA_SINH_VIEN, HO_TEN, NU, NGAY_SINH, SDT_SINH_VIEN, DIA_CHI, MA_LOP, TEN_LOP, LA_SV_BAO_LUU
    ORDER BY MA_SINH_VIEN
    LIMIT v_offset, p_page_size;
    
    -- Xóa bảng tạm
    DROP TEMPORARY TABLE temp_sinh_vien_no_phi;
    
END$$

DELIMITER ;
