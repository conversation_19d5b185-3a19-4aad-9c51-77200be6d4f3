-- =====================================================
-- Script để deploy stored procedure SP_LAY_DS_SINH_VIEN_NO_PHI
-- Ch<PERSON>y script này để tạo stored procedure trong database
-- =====================================================

-- Kiểm tra và tạo stored procedure
USE qldt; -- Thay đổi tên database nếu cần

-- Xóa procedure cũ nếu tồn tại
DROP PROCEDURE IF EXISTS SP_LAY_DS_SINH_VIEN_NO_PHI;

-- Tạo procedure mới
DELIMITER $$

CREATE PROCEDURE SP_LAY_DS_SINH_VIEN_NO_PHI(
    IN p_id_nganh_hoc INT,
    IN p_id_khoa_hoc INT, 
    IN p_id_lop INT,
    IN p_id_tinh_trang INT,
    IN p_keyword VARCHAR(255),
    IN p_page_number INT,
    IN p_page_size INT,
    OUT p_total_records INT
)
BEGIN
    DECLARE v_offset INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        -- Xử lý lỗi: rollback và trả về thông báo lỗi
        ROLLBACK;
        RESIGNAL;
    END;
    
    -- Bắt đầu transaction
    START TRANSACTION;
    
    -- Tính offset cho phân trang
    SET v_offset = p_page_number * p_page_size;
    
    -- Tạo bảng tạm để lưu kết quả
    DROP TEMPORARY TABLE IF EXISTS temp_sinh_vien_no_phi;
    
    CREATE TEMPORARY TABLE temp_sinh_vien_no_phi (
        ID_SINH_VIEN INT,
        MA_SINH_VIEN VARCHAR(20),
        HO_TEN VARCHAR(255),
        NU TINYINT(1),
        NGAY_SINH DATE,
        SDT_SINH_VIEN VARCHAR(15),
        DIA_CHI TEXT,
        MA_LOP VARCHAR(20),
        TEN_LOP VARCHAR(255),
        THANH_TIEN DECIMAL(15,2),
        LA_SV_BAO_LUU TINYINT(1),
        LOAI_PHI VARCHAR(20),
        INDEX idx_id_sinh_vien (ID_SINH_VIEN)
    );
    
    -- 1. Thêm sinh viên nợ học phí thường (HOC_PHI)
    INSERT INTO temp_sinh_vien_no_phi
    SELECT 
        sv.ID_SINH_VIEN,
        sv.MA_SINH_VIEN,
        CONCAT(TRIM(sv.HO_LOT), ' ', TRIM(sv.TEN)) as HO_TEN,
        sv.NU,
        sv.NGAY_SINH,
        sv.SDT_SINH_VIEN,
        CASE 
            WHEN sv.ID_XA IS NULL THEN COALESCE(sv.DIA_CHI, '')
            ELSE CONCAT(
                COALESCE(sv.DIA_CHI, ''), 
                CASE WHEN sv.DIA_CHI IS NOT NULL AND sv.DIA_CHI != '' THEN ', ' ELSE '' END,
                COALESCE(dmXa.TEN_XA, ''), ', ', 
                COALESCE(dmHuyen.TEN_HUYEN, ''), ', ', 
                COALESCE(dmTinh.TEN_TINH, '')
            ) 
        END as DIA_CHI,
        l.MA_LOP,
        l.TEN_LOP,
        COALESCE(hp.MUC_HOC_PHI, 0) as THANH_TIEN,
        COALESCE(sv.LA_SV_BAO_LUU, 0),
        'HOC_PHI' as LOAI_PHI
    FROM SINH_VIEN sv
    LEFT JOIN TINH_TRANG tt ON tt.ID_TINH_TRANG = sv.ID_TINH_TRANG
    LEFT JOIN LOP l ON sv.ID_LOP = l.ID_LOP
    LEFT JOIN KHOI k ON l.ID_KHOI = k.ID_KHOI
    LEFT JOIN HOC_PHI hp ON hp.ID_KHOI = k.ID_KHOI
    LEFT JOIN SV_DONG_HP dhp ON dhp.ID_HOC_PHI = hp.ID_HOC_PHI AND dhp.ID_SINH_VIEN = sv.ID_SINH_VIEN
    LEFT JOIN DM_XA dmXa ON sv.ID_XA = dmXa.ID_XA
    LEFT JOIN DM_HUYEN dmHuyen ON dmXa.ID_HUYEN = dmHuyen.ID_HUYEN
    LEFT JOIN DM_TINH dmTinh ON dmHuyen.ID_TINH = dmTinh.ID_TINH
    WHERE (dhp.ID_CAN_BO IS NULL OR dhp.huy = 1)
        AND hp.ID_HOC_PHI IS NOT NULL
        AND hp.MUC_HOC_PHI > 0
        AND tt.HOAT_DONG = 1
        AND sv.KHOA = 0
        AND (p_id_tinh_trang = -1 OR sv.ID_TINH_TRANG = p_id_tinh_trang)
        AND (p_id_nganh_hoc = -1 OR k.ID_NGANH_HOC = p_id_nganh_hoc)
        AND (p_id_khoa_hoc = -1 OR k.ID_NIEN_KHOA = p_id_khoa_hoc)
        AND (p_id_lop = -1 OR l.ID_LOP = p_id_lop)
        AND (p_keyword IS NULL OR p_keyword = '' OR 
             CONCAT(TRIM(sv.HO_LOT), ' ', TRIM(sv.TEN)) LIKE CONCAT('%', p_keyword, '%') OR 
             sv.MA_SINH_VIEN LIKE CONCAT('%', p_keyword, '%'));
    
    -- 2. Thêm sinh viên nợ phí khác (PHI_KHAC)
    INSERT INTO temp_sinh_vien_no_phi
    SELECT 
        sv.ID_SINH_VIEN,
        sv.MA_SINH_VIEN,
        CONCAT(TRIM(sv.HO_LOT), ' ', TRIM(sv.TEN)) as HO_TEN,
        sv.NU,
        sv.NGAY_SINH,
        sv.SDT_SINH_VIEN,
        CASE 
            WHEN sv.ID_XA IS NULL THEN COALESCE(sv.DIA_CHI, '')
            ELSE CONCAT(
                COALESCE(sv.DIA_CHI, ''), 
                CASE WHEN sv.DIA_CHI IS NOT NULL AND sv.DIA_CHI != '' THEN ', ' ELSE '' END,
                COALESCE(dmXa.TEN_XA, ''), ', ', 
                COALESCE(dmHuyen.TEN_HUYEN, ''), ', ', 
                COALESCE(dmTinh.TEN_TINH, '')
            ) 
        END as DIA_CHI,
        l.MA_LOP,
        l.TEN_LOP,
        CASE 
            WHEN pk.NGAY_KET_THUC IS NOT NULL THEN
                CASE 
                    WHEN CURDATE() < pk.NGAY_BAT_DAU THEN 
                        (PERIOD_DIFF(DATE_FORMAT(pk.NGAY_KET_THUC,'%Y%m'), DATE_FORMAT(pk.NGAY_BAT_DAU,'%Y%m')) + 1) * COALESCE(pk.DON_GIA, 0)
                    ELSE 
                        (PERIOD_DIFF(DATE_FORMAT(pk.NGAY_KET_THUC,'%Y%m'), DATE_FORMAT(CURDATE(),'%Y%m')) + 1) * COALESCE(pk.DON_GIA, 0)
                END
            ELSE COALESCE(pk.DON_GIA, 0)
        END as THANH_TIEN,
        COALESCE(sv.LA_SV_BAO_LUU, 0),
        'PHI_KHAC' as LOAI_PHI
    FROM SINH_VIEN sv
    LEFT JOIN TINH_TRANG tt ON tt.ID_TINH_TRANG = sv.ID_TINH_TRANG
    LEFT JOIN LOP l ON sv.ID_LOP = l.ID_LOP
    LEFT JOIN KHOI k ON l.ID_KHOI = k.ID_KHOI
    LEFT JOIN PHI_KHAC pk ON pk.ID_KHOI = k.ID_KHOI
    LEFT JOIN SV_DONG_PK dpk ON dpk.ID_PHI_KHAC = pk.ID_PHI_KHAC AND dpk.ID_SINH_VIEN = sv.ID_SINH_VIEN
    LEFT JOIN DM_XA dmXa ON sv.ID_XA = dmXa.ID_XA
    LEFT JOIN DM_HUYEN dmHuyen ON dmXa.ID_HUYEN = dmHuyen.ID_HUYEN
    LEFT JOIN DM_TINH dmTinh ON dmHuyen.ID_TINH = dmTinh.ID_TINH
    WHERE (dpk.ID_CAN_BO IS NULL OR dpk.huy = 1)
        AND pk.ID_PHI_KHAC IS NOT NULL
        AND tt.HOAT_DONG = 1
        AND sv.KHOA = 0
        AND (p_id_tinh_trang = -1 OR sv.ID_TINH_TRANG = p_id_tinh_trang)
        AND (p_id_nganh_hoc = -1 OR k.ID_NGANH_HOC = p_id_nganh_hoc)
        AND (p_id_khoa_hoc = -1 OR k.ID_NIEN_KHOA = p_id_khoa_hoc)
        AND (p_id_lop = -1 OR l.ID_LOP = p_id_lop)
        AND (p_keyword IS NULL OR p_keyword = '' OR 
             CONCAT(TRIM(sv.HO_LOT), ' ', TRIM(sv.TEN)) LIKE CONCAT('%', p_keyword, '%') OR 
             sv.MA_SINH_VIEN LIKE CONCAT('%', p_keyword, '%'))
        AND (pk.NGAY_BAT_DAU IS NULL OR pk.NGAY_KET_THUC IS NULL OR 
             (pk.NGAY_BAT_DAU IS NOT NULL AND pk.NGAY_KET_THUC IS NOT NULL AND CURDATE() < pk.NGAY_KET_THUC));
    
    -- Tính tổng số bản ghi
    SELECT COUNT(DISTINCT ID_SINH_VIEN) INTO p_total_records FROM temp_sinh_vien_no_phi;
    
    -- Trả về kết quả với phân trang và tổng tiền theo sinh viên
    SELECT 
        ID_SINH_VIEN,
        MA_SINH_VIEN,
        HO_TEN,
        NU,
        NGAY_SINH,
        SDT_SINH_VIEN,
        DIA_CHI,
        MA_LOP,
        TEN_LOP,
        SUM(THANH_TIEN) as TONG_TIEN,
        LA_SV_BAO_LUU
    FROM temp_sinh_vien_no_phi
    GROUP BY ID_SINH_VIEN, MA_SINH_VIEN, HO_TEN, NU, NGAY_SINH, SDT_SINH_VIEN, DIA_CHI, MA_LOP, TEN_LOP, LA_SV_BAO_LUU
    ORDER BY MA_SINH_VIEN
    LIMIT v_offset, p_page_size;
    
    -- Commit transaction
    COMMIT;
    
    -- Xóa bảng tạm
    DROP TEMPORARY TABLE IF EXISTS temp_sinh_vien_no_phi;
    
END$$

DELIMITER ;

-- Test stored procedure
-- CALL SP_LAY_DS_SINH_VIEN_NO_PHI(-1, -1, -1, -1, '', 0, 10, @total);
-- SELECT @total as total_records;

SELECT 'Stored procedure SP_LAY_DS_SINH_VIEN_NO_PHI created successfully!' as status;
