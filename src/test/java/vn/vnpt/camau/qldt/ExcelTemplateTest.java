package vn.vnpt.camau.qldt;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ClassPathResource;

import java.io.InputStream;

public class ExcelTemplateTest {

    public static void main(String[] args) {
        new ExcelTemplateTest().testReadTemplate();
    }

    public void testReadTemplate() {
        try {
            // Load template file
            ClassPathResource templateResource = new ClassPathResource("static/mau/lich-giang-vien-tuan.xlsx");
            InputStream templateStream = templateResource.getInputStream();
            
            // Create workbook from template
            XSSFWorkbook workbook = new XSSFWorkbook(templateStream);
            Sheet sheet = workbook.getSheetAt(0);
            
            System.out.println("Template loaded successfully!");
            System.out.println("Sheet name: " + sheet.getSheetName());
            System.out.println("Number of rows: " + sheet.getLastRowNum());
            
            // Print first 20 rows to see structure
            for (int i = 0; i <= Math.min(20, sheet.getLastRowNum()); i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    System.out.print("Row " + i + ": ");
                    for (int j = 0; j < 4; j++) {
                        Cell cell = row.getCell(j);
                        if (cell != null) {
                            String cellValue = "";
                            switch (cell.getCellType()) {
                                case Cell.CELL_TYPE_STRING:
                                    cellValue = cell.getStringCellValue();
                                    break;
                                case Cell.CELL_TYPE_NUMERIC:
                                    cellValue = String.valueOf(cell.getNumericCellValue());
                                    break;
                                default:
                                    cellValue = "";
                            }
                            System.out.print("[" + j + "]=" + cellValue.replace("\n", "\\n") + " | ");
                        } else {
                            System.out.print("[" + j + "]=null | ");
                        }
                    }
                    System.out.println();
                }
            }
            
            workbook.close();
            templateStream.close();
            
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("Error reading template: " + e.getMessage());
        }
    }
}
