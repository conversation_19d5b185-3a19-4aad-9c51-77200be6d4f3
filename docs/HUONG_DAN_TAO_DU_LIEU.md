# HƯỚNG DẪN TẠO DỮ LIỆU LỊCH GIẢNG DẠY

## 🎯 Để có dữ liệu hiển thị trong giao diện lịch giảng dạy chi tiết, cần thực hiện theo thứ tự:

### **BƯỚC 1: Tạo dữ liệu cơ bản**

#### 1.1. <PERSON><PERSON><PERSON> khóa
- Vào menu **<PERSON>h mục > <PERSON><PERSON><PERSON> khóa**
- Thêm niên khóa mới (VD: 2024-2025)

#### 1.2. Tạ<PERSON>ọc kỳ
- Vào menu **<PERSON><PERSON> mục > Học kỳ**
- Thêm học kỳ thuộc niên khóa vừa tạo (VD: Học kỳ 1 - 2024-2025)

#### 1.3. Tạo Môn học
- Vào menu **Danh mục > <PERSON><PERSON><PERSON> học**
- Thêm các môn học cần thiết

#### 1.4. <PERSON><PERSON><PERSON> học
- Vào menu **<PERSON><PERSON> mục > Lớp**
- Thêm các lớp học

#### 1.5. T<PERSON><PERSON>òng học
- Vào menu **<PERSON><PERSON> mục > <PERSON>òng học**
- Thêm các phòng học với thông tin:
  - Tên phòng
  - Loại phòng (LT: Lý thuyết, TH: Thực hành)
  - Cơ sở (CS1, CS2)
  - Khu (KHU_LT, KHU_TH)

#### 1.6. Tạo Cán bộ (Giảng viên)
- Vào menu **Danh mục > Cán bộ**
- Thêm giảng viên

### **BƯỚC 2: Tạo Lịch giảng dạy (Lịch tổng quát)**

#### 2.1. Tạo lịch thủ công
- Vào menu **Lịch giảng dạy > Quản lý lịch giảng**
- Click nút **"Thêm lịch giảng"**
- Điền thông tin:
  - Học kỳ
  - Môn học
  - Lớp
  - Giảng viên
  - Hình thức (LT/TH)
  - Ngày giảng
  - Thứ (2-8)
  - Buổi (SANG/CHIEU/TOI)
  - Tiết bắt đầu/kết thúc
  - Phòng học
  - Số tiết

#### 2.2. Tạo lịch cho nhiều tuần
- Sau khi có lịch mẫu, click **"Tạo lịch nhiều tuần"**
- Chọn lịch mẫu
- Nhập danh sách ngày (VD: 2024-01-15, 2024-01-22, 2024-01-29)

### **BƯỚC 3: Tạo Lịch giảng dạy chi tiết**

#### 3.1. Tự động tạo chi tiết
- Vào menu **Lịch giảng dạy > Chi tiết lịch giảng**
- Click **"Tạo tự động"** (nếu có)
- Chọn lịch giảng dạy
- Chọn khoảng thời gian
- Hệ thống sẽ tự động tạo các buổi học chi tiết

#### 3.2. Tạo thủ công
- Click **"Thêm buổi học"**
- Chọn lịch giảng dạy
- Chọn ngày giảng cụ thể
- Điền tiết bắt đầu/kết thúc
- Chọn trạng thái (0: Chưa dạy, 1: Đã dạy, 2: Vắng, 3: Hoãn)

### **BƯỚC 4: Kiểm tra dữ liệu**

#### 4.1. Kiểm tra API
```bash
# Kiểm tra lịch giảng dạy
GET /lich-giang-day/tim-kiem?page=0&size=10

# Kiểm tra chi tiết lịch giảng
GET /lich-giang-day-chi-tiet/tim-kiem?page=0&size=10
```

#### 4.2. Kiểm tra giao diện
- Vào **Lịch giảng dạy > Chi tiết lịch giảng**
- Chọn lịch giảng dạy trong dropdown
- Click **"Tìm kiếm"**
- Dữ liệu sẽ hiển thị nếu đã tạo đúng

## 🔧 Tạo dữ liệu mẫu nhanh

### Script SQL tạo dữ liệu mẫu:
```sql
-- Tạo niên khóa
INSERT INTO NIEN_KHOA (TEN_NIEN_KHOA, NAM_BAT_DAU, NAM_KET_THUC, HOAT_DONG) 
VALUES ('2024-2025', 2024, 2025, 1);

-- Tạo học kỳ
INSERT INTO HOC_KY (TEN_HOC_KY, ID_NIEN_KHOA, NGAY_BAT_DAU, NGAY_KET_THUC, HOAT_DONG) 
VALUES ('Học kỳ 1', 1, '2024-09-01', '2024-12-31', 1);

-- Tạo môn học
INSERT INTO MON_HOC (MA_MON_HOC, TEN_MON_HOC, SO_TIN_CHI, HOAT_DONG) 
VALUES ('JAVA01', 'Lập trình Java', 3, 1);

-- Tạo lớp
INSERT INTO LOP (MA_LOP, TEN_LOP, HOAT_DONG) 
VALUES ('CNTT01', 'Công nghệ thông tin 01', 1);

-- Tạo phòng học
INSERT INTO PHONG_HOC (MA_PHONG, TEN_PHONG, LOAI_PHONG, CO_SO, KHU, HOAT_DONG) 
VALUES ('P101', 'Phòng 101', 'LT', 'CS1', 'KHU_LT', 1);

-- Tạo cán bộ
INSERT INTO CAN_BO (MA_CAN_BO, HO_TEN, EMAIL, HOAT_DONG) 
VALUES ('GV001', 'Nguyễn Văn A', '<EMAIL>', 1);

-- Tạo lịch giảng dạy
INSERT INTO LICH_GIANG_DAY (ID_HOC_KY, ID_MON_HOC, ID_LOP, ID_GIANG_VIEN, 
    HINH_THUC, NGAY_GIANG, THU, BUOI, TIET_BAT_DAU, TIET_KET_THUC, 
    PHONG, SO_TIET, TRANG_THAI) 
VALUES (1, 1, 1, 1, 'LT', '2024-09-02', 2, 'SANG', 1, 4, 'P101', 4, 0);

-- Tạo chi tiết lịch giảng
INSERT INTO LICH_GIANG_DAY_CHI_TIET (ID_LICH_GIANG, NGAY_GIANG, 
    TIET_BAT_DAU, TIET_KET_THUC, TRANG_THAI, SO_SV_VANG) 
VALUES (1, '2024-09-02', 1, 4, 0, 0);
```

## ⚠️ Lưu ý quan trọng

1. **Thứ tự tạo dữ liệu**: Phải tạo theo đúng thứ tự dependency
2. **Quan hệ khóa ngoại**: Đảm bảo các ID tham chiếu đúng
3. **Trạng thái hoạt động**: Các entity phải có HOAT_DONG = 1
4. **Định dạng ngày**: Sử dụng định dạng YYYY-MM-DD
5. **Validation**: Hệ thống sẽ validate dữ liệu khi lưu

## 🎯 Kết quả mong đợi

Sau khi tạo đủ dữ liệu:
- Dropdown "Chọn lịch giảng dạy" sẽ có dữ liệu
- Bảng chi tiết sẽ hiển thị các buổi học
- Có thể tìm kiếm, lọc dữ liệu
- Có thể thêm/sửa/xóa chi tiết
