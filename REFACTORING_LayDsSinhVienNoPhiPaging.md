# Refactoring LayDsSinhVienNoPhiPaging Function

## Problem Statement
The original `LayDsSinhVienNoPhiPaging` function was extremely difficult to maintain due to:
- **Complex UNION query** with 4 different parts
- **Over 100 lines** of concatenated SQL strings
- **Repeated code patterns** across different fee types
- **Hard to debug** when issues arise
- **Difficult to modify** when business logic changes

## Solution Overview
I've created a **new maintainable version** that breaks down the complex query into smaller, focused components:

### 1. **Separate Repository Methods**
Instead of one massive UNION query, we now have 4 focused methods:

```java
// Regular tuition fees (HOC_PHI)
List<Object[]> findStudentsWithUnpaidTuition(...)

// Other fees (PHI_KHAC) 
List<Object[]> findStudentsWithUnpaidOtherFees(...)

// Retake fees (HOC_LAI)
List<Object[]> findStudentsWithUnpaidRetakeFees(...)

// Makeup exam fees (THI_LAI)
List<Object[]> findStudentsWithUnpaidMakeupExamFees(...)
```

### 2. **Service Layer Aggregation**
The new service method `LayDsSinhVienNoPhiPagingV2()` combines results:

```java
public Page<Object[]> LayDsSinhVienNoPhiPagingV2(...) {
    // Get data from each fee type
    List<Object[]> tuitionFees = repo.findStudentsWithUnpaidTuition(...);
    List<Object[]> otherFees = repo.findStudentsWithUnpaidOtherFees(...);
    List<Object[]> retakeFees = repo.findStudentsWithUnpaidRetakeFees(...);
    List<Object[]> makeupExamFees = repo.findStudentsWithUnpaidMakeupExamFees(...);
    
    // Combine and aggregate by student
    Map<Integer, StudentFeeInfo> studentMap = new HashMap<>();
    processFeeList(tuitionFees, studentMap, "TUITION");
    processFeeList(otherFees, studentMap, "OTHER");
    processFeeList(retakeFees, studentMap, "RETAKE");
    processFeeList(makeupExamFees, studentMap, "MAKEUP_EXAM");
    
    // Convert to paginated result
    return applyPagination(results, page);
}
```

## Benefits of New Approach

### ✅ **Maintainability**
- Each query focuses on **one fee type**
- Easy to understand and modify individual parts
- Clear separation of concerns

### ✅ **Debuggability** 
- Can test each fee type query independently
- Easy to identify which part has issues
- Better error handling and logging

### ✅ **Flexibility**
- Easy to add new fee types
- Can modify business logic for specific fee types
- Can optimize individual queries

### ✅ **Readability**
- Named parameters instead of positional (?1, ?2, etc.)
- Clear method names that describe purpose
- Proper formatting and indentation

### ✅ **Testability**
- Can unit test each component separately
- Mock individual repository methods
- Test aggregation logic independently

## Implementation Details

### Repository Layer
```java
// Example: Clean, readable query with named parameters
@Query(value = "SELECT sv.ID_SINH_VIEN, sv.MA_SINH_VIEN, " +
        "CONCAT(sv.HO_LOT, ' ', sv.TEN) as HO_TEN, " +
        "sv.NU, sv.NGAY_SINH, sv.SDT_SINH_VIEN, " +
        "CASE WHEN sv.ID_XA IS NULL THEN sv.DIA_CHI " +
        "     ELSE CONCAT(sv.DIA_CHI, ', ', dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) END as DIA_CHI, " +
        "l.MA_LOP, l.TEN_LOP, hp.MUC_HOC_PHI as THANH_TIEN, sv.LA_SV_BAO_LUU " +
        "FROM SINH_VIEN sv " +
        "LEFT JOIN TINH_TRANG tt ON tt.ID_TINH_TRANG = sv.ID_TINH_TRANG " +
        "LEFT JOIN LOP l ON sv.ID_LOP = l.ID_LOP " +
        "LEFT JOIN KHOI k ON l.ID_KHOI = k.ID_KHOI " +
        "LEFT JOIN HOC_PHI hp ON hp.ID_KHOI = k.ID_KHOI " +
        "LEFT JOIN SV_DONG_HP dhp ON dhp.ID_HOC_PHI = hp.ID_HOC_PHI AND dhp.ID_SINH_VIEN = sv.ID_SINH_VIEN " +
        "WHERE (dhp.ID_CAN_BO IS NULL OR dhp.huy = 1) " +
        "AND hp.ID_HOC_PHI IS NOT NULL " +
        "AND hp.MUC_HOC_PHI > 0 " +
        "AND tt.HOAT_DONG = 1 " +
        "AND sv.KHOA = 0 " +
        "AND (:idTinhTrang = -1 OR sv.ID_TINH_TRANG = :idTinhTrang) " +
        "AND (:idNganhHoc = -1 OR k.ID_NGANH_HOC = :idNganhHoc) " +
        "AND (:idKhoaHoc = -1 OR k.ID_NIEN_KHOA = :idKhoaHoc) " +
        "AND (:idLop = -1 OR l.ID_LOP = :idLop) " +
        "AND (:keyword IS NULL OR :keyword = '' OR " +
        "     CONCAT(TRIM(sv.HO_LOT), ' ', sv.TEN) LIKE %:keyword% OR " +
        "     sv.MA_SINH_VIEN LIKE %:keyword%)", 
        nativeQuery = true)
List<Object[]> findStudentsWithUnpaidTuition(@Param("idNganhHoc") Integer idNganhHoc, 
                                              @Param("idKhoaHoc") Integer idKhoaHoc, 
                                              @Param("idLop") Integer idLop, 
                                              @Param("idTinhTrang") Integer idTinhTrang, 
                                              @Param("keyword") String keyword);
```

### Service Layer
```java
// Helper class for aggregating student data
private static class StudentFeeInfo {
    private Integer idSinhVien;
    private String maSinhVien;
    private String hoTen;
    // ... other fields
    private Double tongTien = 0.0;
    private Set<String> feeTypes = new HashSet<>();

    public void addFee(Double amount, String feeType) {
        if (amount != null && !feeTypes.contains(feeType)) {
            this.tongTien += amount;
            this.feeTypes.add(feeType);
        }
    }
}
```

## Migration Strategy

### Phase 1: Parallel Implementation ✅ DONE
- New methods created alongside existing ones
- Test endpoint `/lay-ds-sinh-vien-v2` available
- Fallback to original method if errors occur

### Phase 2: Testing & Validation
- Compare results between old and new implementations
- Performance testing
- User acceptance testing

### Phase 3: Gradual Migration
- Switch controller to use new method
- Monitor for any issues
- Remove old implementation once stable

## Testing the New Implementation

### Test Endpoint
```
GET /thu-phi/lay-ds-sinh-vien-v2?idNganhHoc=-1&idKhoaHoc=-1&idLop=-1&idTinhTrang=-1&keyword=
```

### Comparison Testing
You can compare results between:
- Original: `/thu-phi/lay-ds-sinh-vien`
- New: `/thu-phi/lay-ds-sinh-vien-v2`

## Files Modified

1. **SvDongHpRepository.java** - Added 4 new focused query methods
2. **SvDongHpService.java** - Added new service interface method
3. **SvDongHpServiceImp.java** - Implemented new maintainable logic
4. **ThuPhiController.java** - Added test endpoint for new implementation

## Next Steps

1. **Test the new implementation** thoroughly
2. **Compare performance** with original version
3. **Validate business logic** is preserved
4. **Switch to new implementation** when ready
5. **Remove old code** after successful migration

The new implementation provides a solid foundation for future maintenance and enhancements while preserving all existing functionality.
